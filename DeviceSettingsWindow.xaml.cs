using System;
using System.Collections.Generic;
using Microsoft.Extensions.DependencyInjection;
using PEMTestSystem.Models.Configuration;
using PEMTestSystem.Services;
using System.IO.Ports;
using System.Linq;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Media;
using System.Data.SqlClient;
using System.Threading.Tasks;

namespace PEMTestSystem
{
    public partial class DeviceSettingsWindow : Window
    {
        private readonly DeviceConfigurationService _configService;
        private SystemDeviceConfiguration _currentConfiguration;
        private bool _isLoading = false;
        private string? _targetDeviceName = null; // 目标设备名称，用于定位到特定设备

        public DeviceSettingsWindow()
        {
            InitializeComponent();

            // 获取配置服务
            var app = (App)Application.Current;
            _configService = app.Host?.Services.GetRequiredService<DeviceConfigurationService>()
                ?? throw new InvalidOperationException("无法获取设备配置服务");

            _currentConfiguration = new SystemDeviceConfiguration();

            InitializeAsync();
        }

        /// <summary>
        /// 构造函数重载，用于打开特定设备的设置
        /// </summary>
        /// <param name="deviceName">设备名称</param>
        public DeviceSettingsWindow(string deviceName) : this()
        {
            _targetDeviceName = deviceName;
            App.AlarmService.Info("设备设置", $"打开设备 {deviceName} 的设置窗口");
        }

        private async void InitializeAsync()
        {
            try
            {
                _isLoading = true;

                InitializeSerialPorts();
                await LoadSettingsAsync();

                // 如果指定了目标设备，自动切换到对应的标签页
                if (!string.IsNullOrEmpty(_targetDeviceName))
                {
                    SwitchToDeviceTab(_targetDeviceName);
                }

                App.AlarmService.Info("设备设置", "设备设置窗口初始化完成");
            }
            catch (Exception ex)
            {
                App.AlarmService.Error("设备设置", "设备设置窗口初始化失败", ex);
                MessageBox.Show($"初始化失败: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
            finally
            {
                _isLoading = false;
            }
        }

        private void InitializeSerialPorts()
        {
            // 获取系统中所有可用的串口
            string[] portNames = _configService.GetAvailableSerialPorts();

            // 为所有串口下拉框添加可用端口
            PowerPortCombo.ItemsSource = portNames;
            TempControllerPortCombo.ItemsSource = portNames;
            FlowPump1PortCombo.ItemsSource = portNames;
            FlowPump2PortCombo.ItemsSource = portNames;

            App.AlarmService.Debug("设备设置", $"检测到 {portNames.Length} 个可用串口: {string.Join(", ", portNames)}");
        }

        private async Task LoadSettingsAsync()
        {
            try
            {
                _currentConfiguration = await _configService.GetConfigurationAsync();

                // 加载温控器设置
                LoadDeviceSettings(_currentConfiguration.TemperatureController,
                    TempControllerPortCombo, TempControllerBaudRateCombo, TempControllerDataBitsCombo,
                    TempControllerStopBitsCombo, TempControllerParityCombo, TempControllerAddressBox,
                    TempControllerStatusText);

                // 加载流量泵1设置
                LoadDeviceSettings(_currentConfiguration.FlowPump1,
                    FlowPump1PortCombo, FlowPump1BaudRateCombo, FlowPump1DataBitsCombo,
                    FlowPump1StopBitsCombo, FlowPump1ParityCombo, FlowPump1AddressBox,
                    FlowPump1StatusText);

                // 加载流量泵2设置
                LoadDeviceSettings(_currentConfiguration.FlowPump2,
                    FlowPump2PortCombo, FlowPump2BaudRateCombo, FlowPump2DataBitsCombo,
                    FlowPump2StopBitsCombo, FlowPump2ParityCombo, FlowPump2AddressBox,
                    FlowPump2StatusText);

                // 加载电源设置
                LoadDeviceSettings(_currentConfiguration.PowerSupply,
                    PowerPortCombo, PowerBaudRateCombo, PowerDataBitsCombo,
                    PowerStopBitsCombo, PowerParityCombo, PowerAddressBox,
                    PowerStatusText);

                // 加载数据库设置
                LoadDatabaseSettings();

                App.AlarmService.Info("设备设置", "设备配置加载完成");
            }
            catch (Exception ex)
            {
                App.AlarmService.Error("设备设置", "加载设备配置失败", ex);
                MessageBox.Show($"加载配置失败: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void LoadDeviceSettings(DeviceConnectionSettings settings,
            ComboBox portCombo, ComboBox baudRateCombo, ComboBox dataBitsCombo,
            ComboBox stopBitsCombo, ComboBox parityCombo, TextBox addressBox,
            TextBlock statusText)
        {
            if (_isLoading) return;

            try
            {
                // 设置串口
                if (portCombo.Items.Contains(settings.PortName))
                {
                    portCombo.SelectedItem = settings.PortName;
                }

                // 设置波特率
                SetComboBoxValue(baudRateCombo, settings.BaudRate.ToString());

                // 设置数据位
                SetComboBoxValue(dataBitsCombo, settings.DataBits.ToString());

                // 设置停止位
                SetComboBoxValue(stopBitsCombo, settings.StopBits.ToString());

                // 设置校验位
                SetComboBoxValue(parityCombo, settings.Parity.ToString());

                // 设置设备地址
                addressBox.Text = settings.DeviceAddress.ToString();

                // 设置状态显示
                UpdateStatusDisplay(statusText, settings);
            }
            catch (Exception ex)
            {
                App.AlarmService.Error("设备设置", $"加载设备 {settings.DeviceName} 设置失败", ex);
            }
        }

        private void SetComboBoxValue(ComboBox comboBox, string value)
        {
            foreach (ComboBoxItem item in comboBox.Items)
            {
                if (item.Content?.ToString() == value)
                {
                    comboBox.SelectedItem = item;
                    break;
                }
            }
        }

        private void UpdateStatusDisplay(TextBlock statusText, DeviceConnectionSettings settings)
        {
            if (settings.LastTestTime.HasValue)
            {
                statusText.Text = $"{settings.LastTestResult} ({settings.LastTestTime:HH:mm:ss})";
                statusText.Foreground = settings.Status == DeviceConnectionStatus.Connected
                    ? FindResource("SuccessColor") as SolidColorBrush
                    : FindResource("DangerColor") as SolidColorBrush;
            }
            else
            {
                statusText.Text = "未测试";
                statusText.Foreground = FindResource("TextColor") as SolidColorBrush;
            }
        }

        private void LoadDatabaseSettings()
        {
            // TODO: 从配置中加载数据库设置
            // 这里使用默认值
        }

        /// <summary>
        /// 根据设备名称切换到对应的标签页
        /// </summary>
        /// <param name="deviceName">设备名称</param>
        private void SwitchToDeviceTab(string deviceName)
        {
            try
            {
                int targetTabIndex = deviceName switch
                {
                    "直流电源" => 0,      // 电源标签页
                    "温控仪" => 1,        // 温控器标签页
                    "流量泵 1" => 2,      // 流量泵1标签页
                    "流量泵 2" => 3,      // 流量泵2标签页
                    "数据库" => 4,        // 数据库标签页
                    _ => 0                // 默认显示电源标签页
                };

                // 切换到对应的标签页
                DeviceTabControl.SelectedIndex = targetTabIndex;

                App.AlarmService.Debug("设备设置", $"切换到设备 {deviceName} 的设置标签页 (索引: {targetTabIndex})");
            }
            catch (Exception ex)
            {
                App.AlarmService.Error("设备设置", $"切换到设备 {deviceName} 标签页失败", ex);
            }
        }

        private void SaveDeviceSettings(DeviceConnectionSettings settings,
            ComboBox portCombo, ComboBox baudRateCombo, ComboBox dataBitsCombo,
            ComboBox stopBitsCombo, ComboBox parityCombo, TextBox addressBox)
        {
            try
            {
                // 保存串口设置
                if (portCombo.SelectedItem != null)
                {
                    settings.PortName = portCombo.SelectedItem.ToString() ?? "COM1";
                }

                // 保存波特率
                if (baudRateCombo.SelectedItem is ComboBoxItem baudRateItem)
                {
                    if (int.TryParse(baudRateItem.Content?.ToString(), out int baudRate))
                    {
                        settings.BaudRate = baudRate;
                    }
                }

                // 保存数据位
                if (dataBitsCombo.SelectedItem is ComboBoxItem dataBitsItem)
                {
                    if (int.TryParse(dataBitsItem.Content?.ToString(), out int dataBits))
                    {
                        settings.DataBits = dataBits;
                    }
                }

                // 保存停止位
                if (stopBitsCombo.SelectedItem is ComboBoxItem stopBitsItem)
                {
                    if (Enum.TryParse<StopBits>(stopBitsItem.Content?.ToString(), out StopBits stopBits))
                    {
                        settings.StopBits = stopBits;
                    }
                }

                // 保存校验位
                if (parityCombo.SelectedItem is ComboBoxItem parityItem)
                {
                    if (Enum.TryParse<Parity>(parityItem.Content?.ToString(), out Parity parity))
                    {
                        settings.Parity = parity;
                    }
                }

                // 保存设备地址
                if (byte.TryParse(addressBox.Text, out byte address))
                {
                    settings.DeviceAddress = address;
                }
            }
            catch (Exception ex)
            {
                App.AlarmService.Error("设备设置", $"保存设备 {settings.DeviceName} 设置失败", ex);
            }
        }

        private async Task SaveSettingsAsync()
        {
            try
            {
                // 保存温控器设置
                SaveDeviceSettings(_currentConfiguration.TemperatureController,
                    TempControllerPortCombo, TempControllerBaudRateCombo, TempControllerDataBitsCombo,
                    TempControllerStopBitsCombo, TempControllerParityCombo, TempControllerAddressBox);

                // 保存流量泵1设置
                SaveDeviceSettings(_currentConfiguration.FlowPump1,
                    FlowPump1PortCombo, FlowPump1BaudRateCombo, FlowPump1DataBitsCombo,
                    FlowPump1StopBitsCombo, FlowPump1ParityCombo, FlowPump1AddressBox);

                // 保存流量泵2设置
                SaveDeviceSettings(_currentConfiguration.FlowPump2,
                    FlowPump2PortCombo, FlowPump2BaudRateCombo, FlowPump2DataBitsCombo,
                    FlowPump2StopBitsCombo, FlowPump2ParityCombo, FlowPump2AddressBox);

                // 保存电源设置
                SaveDeviceSettings(_currentConfiguration.PowerSupply,
                    PowerPortCombo, PowerBaudRateCombo, PowerDataBitsCombo,
                    PowerStopBitsCombo, PowerParityCombo, PowerAddressBox);

                // 验证配置
                var (isValid, errors) = _configService.ValidateConfiguration(_currentConfiguration);
                if (!isValid)
                {
                    var errorMessage = "配置验证失败:\n" + string.Join("\n", errors);
                    MessageBox.Show(errorMessage, "配置错误", MessageBoxButton.OK, MessageBoxImage.Warning);
                    return;
                }

                // 保存配置
                var success = await _configService.SaveConfigurationAsync(_currentConfiguration);
                if (success)
                {
                    MessageBox.Show("设置保存成功！", "成功", MessageBoxButton.OK, MessageBoxImage.Information);
                    App.AlarmService.Info("设备设置", "设备配置保存成功");
                }
                else
                {
                    MessageBox.Show("设置保存失败！", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
                }
            }
            catch (Exception ex)
            {
                App.AlarmService.Error("设备设置", "保存设备配置失败", ex);
                MessageBox.Show($"保存失败: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        #region 通讯测试事件处理

        private async void TestPowerButton_Click(object sender, RoutedEventArgs e)
        {
            await TestDeviceCommunication("电源", PowerPortCombo.Text, PowerBaudRateCombo.Text, 
                PowerDataBitsCombo.Text, PowerStopBitsCombo.Text, PowerParityCombo.Text, 
                PowerAddressBox.Text, PowerStatusText);
        }

        private async void TestTempControllerButton_Click(object sender, RoutedEventArgs e)
        {
            await TestDeviceCommunication("温控仪", TempControllerPortCombo.Text, TempControllerBaudRateCombo.Text,
                TempControllerDataBitsCombo.Text, TempControllerStopBitsCombo.Text, TempControllerParityCombo.Text,
                TempControllerAddressBox.Text, TempControllerStatusText);
        }

        private async void TestFlowPump1Button_Click(object sender, RoutedEventArgs e)
        {
            await TestDeviceCommunication("流量泵1", FlowPump1PortCombo.Text, FlowPump1BaudRateCombo.Text,
                FlowPump1DataBitsCombo.Text, FlowPump1StopBitsCombo.Text, FlowPump1ParityCombo.Text,
                FlowPump1AddressBox.Text, FlowPump1StatusText);
        }

        private async void TestFlowPump2Button_Click(object sender, RoutedEventArgs e)
        {
            await TestDeviceCommunication("流量泵2", FlowPump2PortCombo.Text, FlowPump2BaudRateCombo.Text,
                FlowPump2DataBitsCombo.Text, FlowPump2StopBitsCombo.Text, FlowPump2ParityCombo.Text,
                FlowPump2AddressBox.Text, FlowPump2StatusText);
        }

        private async Task TestDeviceCommunication(string deviceName, string port, string baudRate,
            string dataBits, string stopBits, string parity, string address, TextBlock statusText)
        {
            if (string.IsNullOrEmpty(port))
            {
                statusText.Text = "请选择串口";
                statusText.Foreground = FindResource("DangerColor") as SolidColorBrush;
                return;
            }

            statusText.Text = "测试中...";
            statusText.Foreground = FindResource("PrimaryColor") as SolidColorBrush;

            try
            {
                // 创建临时设备配置进行测试
                var deviceSettings = new DeviceConnectionSettings
                {
                    DeviceName = deviceName,
                    PortName = port,
                    BaudRate = int.Parse(baudRate),
                    DataBits = int.Parse(dataBits),
                    StopBits = (StopBits)Enum.Parse(typeof(StopBits), stopBits),
                    Parity = (Parity)Enum.Parse(typeof(Parity), parity),
                    DeviceAddress = byte.Parse(address)
                };

                // 使用配置服务进行测试
                var (success, message) = await _configService.TestDeviceConnectionAsync(deviceSettings);

                statusText.Text = message;
                statusText.Foreground = success
                    ? FindResource("SuccessColor") as SolidColorBrush
                    : FindResource("DangerColor") as SolidColorBrush;

                App.AlarmService.Info("设备测试", $"设备 {deviceName} 测试结果: {message}");
            }
            catch (Exception ex)
            {
                var errorMessage = $"测试异常: {ex.Message}";
                statusText.Text = errorMessage;
                statusText.Foreground = FindResource("DangerColor") as SolidColorBrush;
                App.AlarmService.Error("设备测试", $"设备 {deviceName} 测试异常", ex);
            }
        }



        private async void TestDatabaseButton_Click(object sender, RoutedEventArgs e)
        {
            DatabaseStatusText.Text = "测试中...";
            DatabaseStatusText.Foreground = FindResource("PrimaryColor") as SolidColorBrush;

            try
            {
                string connectionString = BuildConnectionString();
                
                using (SqlConnection connection = new SqlConnection(connectionString))
                {
                    await connection.OpenAsync();
                    
                    DatabaseStatusText.Text = "连接成功";
                    DatabaseStatusText.Foreground = FindResource("SuccessColor") as SolidColorBrush;
                }
            }
            catch (Exception ex)
            {
                DatabaseStatusText.Text = $"连接失败: {ex.Message}";
                DatabaseStatusText.Foreground = FindResource("DangerColor") as SolidColorBrush;
            }
        }

        private string BuildConnectionString()
        {
            string server = DatabaseServerBox.Text.Trim();
            string database = DatabaseNameBox.Text.Trim();
            
            if (UseWindowsAuthCheckBox.IsChecked == true)
            {
                return $"Server={server};Database={database};Integrated Security=true;Connection Timeout=30;";
            }
            else
            {
                string username = DatabaseUserBox.Text.Trim();
                string password = DatabasePasswordBox.Password;
                return $"Server={server};Database={database};User Id={username};Password={password};Connection Timeout=30;";
            }
        }

        #endregion

        #region 界面事件处理

        private void UseWindowsAuth_Checked(object sender, RoutedEventArgs e)
        {
            DatabaseUserBox.IsEnabled = false;
            DatabasePasswordBox.IsEnabled = false;
        }

        private void UseWindowsAuth_Unchecked(object sender, RoutedEventArgs e)
        {
            DatabaseUserBox.IsEnabled = true;
            DatabasePasswordBox.IsEnabled = true;
        }

        private async void SaveButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                await SaveSettingsAsync();
                this.DialogResult = true;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"保存设置失败: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void CancelButton_Click(object sender, RoutedEventArgs e)
        {
            this.DialogResult = false;
        }

        #endregion
    }
} 